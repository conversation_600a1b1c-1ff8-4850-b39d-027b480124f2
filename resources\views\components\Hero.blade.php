<div class="flex w-full bg-black  pt-[80px] lg:pt-[184px]">
    <div class="relative w-full h-[500px] lg:h-[700px] bg-cover bg-center"
        style="background-image: url('{{ asset('frontend-images/gown-backdrop.jpg') }}'); background-attachment: fixed;">

        <div class="absolute inset-0 bg-black opacity-20"></div>

        <div class="absolute inset-0 flex flex-col items-center justify-center z-10">

            <h1 class="text-white text-3xl lg:text-5xl pb-6" style="font-family: 'Playfair Display', serif;">
                Wear the moment. Rent with Ease.
            </h1>
            <div
                class="w-auto px-6 py-3 flex flex-row items-center justify-center hover:bg-purple-600 hover:border-purple-600 transition-colors duration-600 ease-in-out border border-white group">

                <p class="text-white text-semibold text-center group-hover:text-white mr-2"> View all Collections
                </p>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor"
                    class="size-4 text-white group-hover:text-white hover:cursor-pointer">
                    <path fill-rule="evenodd"
                        d="M2 8a.75.75 0 0 1 .75-.75h8.69L8.22 4.03a.75.75 0 0 1 1.06-1.06l4.5 4.5a.75.75 0 0 1 0 1.06l-4.5 4.5a.75.75 0 0 1-1.06-1.06l3.22-3.22H2.75A.75.75 0 0 1 2 8Z"
                        clip-rule="evenodd" />
                </svg>
            </div>
        </div>
    </div>
</div>